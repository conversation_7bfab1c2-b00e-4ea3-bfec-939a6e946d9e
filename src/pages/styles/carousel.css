/* Main container for the carousel */
.outer-carousel-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    padding: 0;
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden; /* Prevent arrow overflow on small screens */
}

/* Inner container */
.carousel-container {
    position: relative;
    width: 100%;
    overflow: visible;
}

/* The carousel itself */
.skills-carousel {
    position: relative;
    width: 100%;
    margin: 0 auto;
}

/* Navigation buttons wrapper */
.nav-buttons-wrapper {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 1000 !important;
    width: 50px !important;
    height: 50px !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Position the left button */
.nav-buttons-wrapper:first-of-type {
    left: -50px !important;
}

/* Position the right button */
.nav-buttons-wrapper:last-of-type {
    right: -50px !important;
}

/* Style the navigation buttons */
.carousel-nav-btn {
    background: var(--glass-hover) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 50% !important;
    width: 45px !important;
    height: 45px !important;
    border: 1px solid var(--glass-border) !important;
    box-shadow: none !important;
    transition: all 0.2s ease !important;
    color: var(--icon-default) !important;
    font-size: 1.5rem !important;
}

.carousel-nav-btn:hover {
    background: var(--glass-active) !important;
    border-color: var(--glass-border-hover) !important;
    color: var(--icon-hover) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3) !important;
}

/* Carousel slide - Consistent with contact section width */
.carousel-slide {
    padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px - optimized padding for content density */
    box-sizing: border-box;
    min-height: 340px; /* Optimized height for better content density */
    height: 340px; /* Fixed height for consistency */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%; /* Increased width to prevent text cutoff */
    margin: 0 auto;

}

/* Indicator container */
.carousel-indicators {
    margin-top: 20px !important;
    margin-bottom: 10px !important;
    display: flex !important;
    justify-content: center !important;
}

/* Removed forced first-child active state - this was causing the bug */

/* iOS-Inspired Carousel Dots - Inactive State */
.carousel-indicators button,
.skills-carousel button[class*="Mui"] {
    min-width: 8px !important;
    width: 8px !important;
    height: 8px !important;
    padding: 6px !important;
    margin: 6px 4px !important;
    border-radius: 50% !important;
    background: rgba(142, 142, 147, 0.3) !important; /* iOS tertiary label */
    border: none !important;
    backdrop-filter: blur(20px) !important;
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
    opacity: 0.7 !important;
}

/* Hover State */
.carousel-indicators button:hover,
.skills-carousel button[class*="Mui"]:hover {
    background: rgba(142, 142, 147, 0.5) !important;
    transform: scale(1.1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.16) !important;
    opacity: 0.9 !important;
}

/* iOS-Inspired Active State */
.carousel-indicators button.carousel-active,
.skills-carousel button.carousel-active {
    background: #ffffff !important; /* Pure white for iOS authenticity */
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    transform: scale(1.3) !important;
    box-shadow:
        0 3px 15px rgba(0, 0, 0, 0.25) !important,
        0 1px 6px rgba(0, 0, 0, 0.15) !important;
    opacity: 1 !important;
    backdrop-filter: blur(20px) !important;
}

/* iOS-Inspired Focus States for Accessibility Compliance */
.MuiButton-root:focus-visible {
    outline: 2px solid rgba(0, 122, 255, 0.8) !important; /* iOS blue focus ring */
    outline-offset: 2px !important;
}

/* Make sure the carousel paper has no background */
.MuiPaper-root {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* Ensure the projects list container is properly sized */
.projects-list {
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: visible;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Media query for large desktop screens - Consistent with contact section */
@media (min-width: 1200px) {
    .carousel-slide {
        width: 90%; /* Increased width to prevent text cutoff */
        min-height: 320px; /* Optimized height for content density */
        height: 320px;
        padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px - minimal padding for content focus */
        align-items: flex-start;
        justify-content: flex-start;
    }
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    .outer-carousel-container {
        width: 100%;
        padding: 0;
    }

    .projects-list {
        padding: 0;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -30px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -30px !important;
    }

    .carousel-nav-btn {
        width: 34px !important;
        height: 34px !important;
        font-size: 1rem !important;
        background: var(--glass-active) !important;
    }

    .carousel-slide {
        min-height: 400px;
        height: 400px; /* Fixed height for consistency */
        width: 95%; /* Increased width to prevent text cutoff */
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px - more horizontal padding */
        align-items: flex-start;
        justify-content: flex-start;
    }
}

/* Media query for very small screens (mobile phones) - Hide arrows, focus on swipe */
@media (max-width: 480px) {
    /* Hide navigation arrows on mobile for cleaner swipe experience */
    .nav-buttons-wrapper {
        display: none !important;
    }

    .carousel-slide {
        min-height: 350px; /* Reduced height to remove empty space */
        height: 350px;
        width: 100%; /* Full width to prevent text cutoff */
        padding: var(--spacing-xs) var(--spacing-sm); /* 0.25rem 0.5rem = 4px 8px - reduced padding for more content space */
        margin: 0 auto; /* Center the slide */
        overflow: visible; /* Allow content to be fully visible */
        box-sizing: border-box; /* Include padding in height calculations */
        align-items: flex-start;
        justify-content: flex-start;
    }

    /* Enhanced carousel dots for mobile */
    .carousel-indicators button,
    .skills-carousel button[class*="Mui"] {
        min-width: 10px !important;
        width: 10px !important;
        height: 10px !important;
        padding: 8px !important;
        margin: 8px 5px !important;
    }

    /* Mobile active state */
    .carousel-indicators button.carousel-active,
    .skills-carousel button.carousel-active {
        transform: scale(1.4) !important;
    }
}

/* Media query for very small mobile screens - Consolidated breakpoint */
@media (max-width: 420px) {
    .nav-buttons-wrapper {
        display: flex !important;
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 10 !important;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -40px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -40px !important;
    }

    .carousel-slide {
        min-height: 160px !important; /* Drastically reduced to eliminate all empty space */
        height: 160px !important;
        width: 100% !important;
        padding: 2px var(--spacing-xs) !important; /* Minimal padding for maximum content */
        margin: 0 auto !important;
        overflow: visible !important;
        box-sizing: border-box !important;
        align-items: flex-start !important;
        justify-content: flex-start !important;
    }

    /* Override carousel library constraints */
    .skills-carousel,
    .skills-carousel > div,
    .skills-carousel .MuiPaper-root,
    .projects-list {
        width: 100% !important;
        max-width: none !important;
        overflow: visible !important;
    }
}

/* Dark mode styles */
.dark .carousel-nav-btn {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border) !important;
    color: var(--dark-icon-default) !important;
}

.dark .carousel-nav-btn:hover {
    background: var(--dark-glass-active) !important;
    border-color: var(--dark-glass-border-hover) !important;
    color: var(--dark-icon-hover) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2) !important;
}

/* Dark mode carousel dots now use the same styling as light mode for consistency */
