/* Main container for the carousel */
.outer-carousel-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    padding: 0;
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden; /* Prevent arrow overflow on small screens */
}

/* Inner container */
.carousel-container {
    position: relative;
    width: 100%;
    overflow: visible;
}

/* The carousel itself */
.skills-carousel {
    position: relative;
    width: 100%;
    margin: 0 auto;
}

/* Navigation buttons wrapper */
.nav-buttons-wrapper {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 1000 !important;
    width: 50px !important;
    height: 50px !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Position the left button */
.nav-buttons-wrapper:first-of-type {
    left: -50px !important;
}

/* Position the right button */
.nav-buttons-wrapper:last-of-type {
    right: -50px !important;
}

/* Style the navigation buttons */
.carousel-nav-btn {
    background: var(--glass-hover) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 50% !important;
    width: 45px !important;
    height: 45px !important;
    border: 1px solid var(--glass-border) !important;
    box-shadow: none !important;
    transition: all 0.2s ease !important;
    color: var(--icon-default) !important;
    font-size: 1.5rem !important;
}

.carousel-nav-btn:hover {
    background: var(--glass-active) !important;
    border-color: var(--glass-border-hover) !important;
    color: var(--icon-hover) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3) !important;
}

/* Carousel slide - Consistent with contact section width */
.carousel-slide {
    padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px - optimized padding for content density */
    box-sizing: border-box;
    min-height: 340px; /* Optimized height for better content density */
    height: 340px; /* Fixed height for consistency */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%; /* Increased width to prevent text cutoff */
    margin: 0 auto;

}

/* Indicator container */
.carousel-indicators {
    margin-top: 20px !important;
    margin-bottom: 10px !important;
    display: flex !important;
    justify-content: center !important;
}

/* Removed forced first-child active state - this was causing the bug */

/* Indicator buttons */
.MuiButton-root {
    min-width: 10px !important;
    width: 10px !important;
    height: 10px !important;
    padding: 6px !important;
    margin: 6px 4px !important;
    border-radius: 50% !important;
    background: var(--glass-base) !important;
    border: 1px solid var(--glass-border) !important;
    backdrop-filter: blur(15px) !important;
    transition: all var(--transition-normal) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.MuiButton-root:hover {
    background: var(--glass-hover) !important;
    border-color: var(--glass-border-hover) !important;
    transform: scale(1.15) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Active state for carousel indicators - Monochromatic design with high contrast */
.MuiButton-root.active,
.MuiButton-root[aria-pressed="true"],
.MuiButton-root.Mui-selected,
.MuiButton-root.MuiButton-contained,
.carousel-indicators .MuiButton-root[aria-current="true"],
.carousel-indicators .MuiButton-root[data-testid*="active"],
.carousel-indicators .MuiButton-root:focus,
.carousel-indicators .MuiButton-root[tabindex="0"],
/* Additional react-material-ui-carousel specific selectors */
.carousel-indicators .MuiButton-root[class*="active"],
.carousel-indicators .MuiButton-root[class*="selected"],
.skills-carousel .MuiButton-root[aria-selected="true"],
.skills-carousel .MuiButton-root.MuiButton-containedPrimary,
.carousel-indicators .MuiButton-root.carousel-active,
.carousel-indicators .MuiButton-root[data-active="true"] {
    background: #27272a !important;
    border-color: #27272a !important;
    transform: scale(1.4) !important;
    box-shadow:
        0 4px 20px rgba(39, 39, 42, 0.5) !important,
        0 2px 10px rgba(39, 39, 42, 0.3) !important,
        0 0 0 3px rgba(255, 255, 255, 0.4) !important;
    opacity: 1 !important;
}

/* Focus states for accessibility compliance */
.MuiButton-root:focus-visible {
    outline: 2px solid #27272a !important;
    outline-offset: 2px !important;
}

.dark .MuiButton-root:focus-visible {
    outline: 2px solid #f1f5f9 !important;
    outline-offset: 2px !important;
}

/* Make sure the carousel paper has no background */
.MuiPaper-root {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* Ensure the projects list container is properly sized */
.projects-list {
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: visible;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Media query for large desktop screens - Consistent with contact section */
@media (min-width: 1200px) {
    .carousel-slide {
        width: 90%; /* Increased width to prevent text cutoff */
        min-height: 320px; /* Optimized height for content density */
        height: 320px;
        padding: var(--spacing-sm) var(--spacing-md); /* 0.5rem 1rem = 8px 16px - minimal padding for content focus */
        align-items: flex-start;
        justify-content: flex-start;
    }
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    .outer-carousel-container {
        width: 100%;
        padding: 0;
    }

    .projects-list {
        padding: 0;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -30px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -30px !important;
    }

    .carousel-nav-btn {
        width: 34px !important;
        height: 34px !important;
        font-size: 1rem !important;
        background: var(--glass-active) !important;
    }

    .carousel-slide {
        min-height: 400px;
        height: 400px; /* Fixed height for consistency */
        width: 95%; /* Increased width to prevent text cutoff */
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px - more horizontal padding */
        align-items: flex-start;
        justify-content: flex-start;
    }
}

/* Media query for very small screens (mobile phones) - Hide arrows, focus on swipe */
@media (max-width: 480px) {
    /* Hide navigation arrows on mobile for cleaner swipe experience */
    .nav-buttons-wrapper {
        display: none !important;
    }

    .carousel-slide {
        min-height: 350px; /* Reduced height to remove empty space */
        height: 350px;
        width: 100%; /* Full width to prevent text cutoff */
        padding: var(--spacing-xs) var(--spacing-sm); /* 0.25rem 0.5rem = 4px 8px - reduced padding for more content space */
        margin: 0 auto; /* Center the slide */
        overflow: visible; /* Allow content to be fully visible */
        box-sizing: border-box; /* Include padding in height calculations */
        align-items: flex-start;
        justify-content: flex-start;
    }

    /* Enhanced carousel dots for mobile swipe navigation */
    .MuiButton-root {
        min-width: 10px !important; /* Slightly larger for better touch targets */
        width: 10px !important; /* Larger for better mobile visibility */
        height: 10px !important;
        padding: 6px !important; /* Enhanced touch area */
        margin: 6px 4px !important; /* Better spacing for touch */
    }

    /* Mobile active state scaling */
    .MuiButton-root.active,
    .MuiButton-root.carousel-active,
    .MuiButton-root[data-active="true"] {
        transform: scale(1.3) !important;
    }

    .dark .MuiButton-root.active,
    .dark .MuiButton-root.carousel-active,
    .dark .MuiButton-root[data-active="true"] {
        transform: scale(1.3) !important;
    }
}

/* Media query for very small mobile screens - Consolidated breakpoint */
@media (max-width: 420px) {
    .nav-buttons-wrapper {
        display: flex !important;
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 10 !important;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -40px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -40px !important;
    }

    .carousel-slide {
        min-height: 160px !important; /* Drastically reduced to eliminate all empty space */
        height: 160px !important;
        width: 100% !important;
        padding: 2px var(--spacing-xs) !important; /* Minimal padding for maximum content */
        margin: 0 auto !important;
        overflow: visible !important;
        box-sizing: border-box !important;
        align-items: flex-start !important;
        justify-content: flex-start !important;
    }

    /* Override carousel library constraints */
    .skills-carousel,
    .skills-carousel > div,
    .skills-carousel .MuiPaper-root,
    .projects-list {
        width: 100% !important;
        max-width: none !important;
        overflow: visible !important;
    }
}

/* Dark mode styles */
.dark .carousel-nav-btn {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border) !important;
    color: var(--dark-icon-default) !important;
}

.dark .carousel-nav-btn:hover {
    background: var(--dark-glass-active) !important;
    border-color: var(--dark-glass-border-hover) !important;
    color: var(--dark-icon-hover) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2) !important;
}

.dark .MuiButton-root {
    background: var(--dark-glass-base) !important;
    border-color: var(--dark-glass-border) !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4) !important;
}

.dark .MuiButton-root:hover {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border-hover) !important;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3) !important,
        0 0 15px rgba(209, 213, 219, 0.2) !important; /* Light silver glow */
    transform: scale(1.15) !important;
}

/* Dark mode active state for carousel indicators - Match light mode appearance */
.dark .MuiButton-root.active,
.dark .MuiButton-root[aria-pressed="true"],
.dark .MuiButton-root.Mui-selected,
.dark .MuiButton-root.MuiButton-contained,
.dark .carousel-indicators .MuiButton-root[aria-current="true"],
.dark .carousel-indicators .MuiButton-root[data-testid*="active"],
.dark .carousel-indicators .MuiButton-root:focus,
.dark .carousel-indicators .MuiButton-root[tabindex="0"],
/* Additional react-material-ui-carousel specific selectors for dark mode */
.dark .carousel-indicators .MuiButton-root[class*="active"],
.dark .carousel-indicators .MuiButton-root[class*="selected"],
.dark .skills-carousel .MuiButton-root[aria-selected="true"],
.dark .skills-carousel .MuiButton-root.MuiButton-containedPrimary,
.dark .carousel-indicators .MuiButton-root.carousel-active,
.dark .carousel-indicators .MuiButton-root[data-active="true"] {
    background: #27272a !important; /* Same dark color as light mode */
    border-color: #27272a !important; /* Same dark border as light mode */
    transform: scale(1.4) !important;
    box-shadow:
        0 4px 20px rgba(39, 39, 42, 0.5) !important,
        0 2px 10px rgba(39, 39, 42, 0.3) !important,
        0 0 0 3px rgba(255, 255, 255, 0.4) !important; /* White ring for dark mode */
    opacity: 1 !important;
}
