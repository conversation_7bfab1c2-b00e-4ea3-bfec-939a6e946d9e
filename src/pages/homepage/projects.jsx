import {motion} from 'motion/react';
import INFO from '../../data/user.js';
import React, { useState, useEffect } from 'react';
import Carousel from 'react-material-ui-carousel';
import Project from '../../components/projects/project.jsx';
import '../styles/carousel.css';

function Projects() {
	const [currentIndex, setCurrentIndex] = useState(0);

	// Handle carousel changes and update active dots
	const handleCarouselChange = (index) => {
		setCurrentIndex(index);
	};

	// Debug and update active dots
	useEffect(() => {
		const updateActiveDots = () => {
			// Try multiple selectors to find the indicators
			const selectors = [
				'.carousel-indicators .MuiButton-root',
				'.carousel-indicators button',
				'[class*="carousel"] button',
				'[class*="indicator"] button',
				'.MuiButton-root',
				'button[class*="Mui"]'
			];

			let indicators = [];
			for (const selector of selectors) {
				indicators = document.querySelectorAll(selector);
				console.log(`Selector "${selector}" found:`, indicators.length, 'elements');
				if (indicators.length > 0) break;
			}

			if (indicators.length === 0) {
				console.log('No indicators found with any selector. DOM structure:');
				const carouselContainer = document.querySelector('.skills-carousel');
				if (carouselContainer) {
					console.log('Carousel container HTML:', carouselContainer.innerHTML);
				}
				return;
			}

			console.log('Using indicators:', indicators.length);
			indicators.forEach((indicator, index) => {
				// Remove all active classes
				indicator.classList.remove('force-active', 'carousel-active');

				// Add active class to current index
				if (index === currentIndex) {
					indicator.classList.add('force-active');
					console.log(`Set indicator ${index} as active`);
				}
			});
		};

		// Try multiple times with increasing delays
		const timeouts = [100, 500, 1000, 2000].map(delay =>
			setTimeout(updateActiveDots, delay)
		);

		return () => timeouts.forEach(clearTimeout);
	}, [currentIndex]);

	return (
		<React.Fragment>
			<section id="projects" className="scroll-child">
				<div className="white-space"></div>
				<motion.div initial={{opacity: 0}}
				            whileInView={{opacity: 1}}
				            transition={{duration: 0.75}}
				            className="homepage-container bg-blur">
					<div className="title projects-title">
						<h2>Skills</h2>
					</div>

					<div className="projects-list" role="region" aria-label="Skills carousel">
						<Carousel
							navButtonsAlwaysVisible={true}
							navButtonsProps={{
								className: "carousel-nav-btn"
							}}
							navButtonsWrapperProps={{
								className: "nav-buttons-wrapper"
							}}
							indicatorContainerProps={{
								className: "carousel-indicators"
							}}
							indicatorIconButtonProps={{
								className: "carousel-indicator-btn"
							}}
							className="skills-carousel"
							animation="slide"
							autoPlay={false}
							interval={6000}
							fullHeightHover={false}
							indicators={true}
							cycleNavigation={true}
							swipe={true}
							onChange={handleCarouselChange}
							index={currentIndex}
							style={{ width: '100%' }}
						>
							{INFO.projects.map((project, index) => (
								<div className="carousel-slide" key={index}>
									<Project
										logo={project.logo}
										title={project.title}
										description={project.description}
									/>
								</div>
							))}
						</Carousel>
					</div>
				</motion.div>
			</section>
		</React.Fragment>
	);
}

export default Projects;
